export interface ModerationRequest {
  reason: string;
  requestedBy: string;
  requestDate: string;
  notes?: string;
}

export interface ReviewComment {
  id: string;
  comment: string;
  createdAt: string;
  createdBy: string;
  type: 'note' | 'status_change' | 'moderation';
}

export interface TReview {
  id: string;
  reviewerName: string;
  reviewerEmail: string;
  targetType: 'agent' | 'agency';
  targetName: string;
  targetId: string;
  rating: number;
  comment: string;
  status: 'published' | 'pending' | 'flagged' | 'hidden';
  moderationRequest?: ModerationRequest;
  createdAt: string;
  updatedAt: string;
  hiddenReason?: string;
  comments?: ReviewComment[];
}

export interface ReviewStats {
  total: number;
  published: number;
  pending: number;
  flagged: number;
  hidden: number;
  averageRating: number;
  replied: number;
}

// Legacy type for backward compatibility
export type TReviewLegacy = {
  id: string;
  rating: number;
  review: string;
  user: {
    id: string;
    name: string;
    image: string;
  };
  property: {
    id: string;
    name: string;
  };
  createdAt: string;
};
