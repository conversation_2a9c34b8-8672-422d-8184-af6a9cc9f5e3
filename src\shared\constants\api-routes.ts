// export const BASE_URL =  'https://api.findanyagent.ae';
export const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';
export const URL = BASE_URL + '/api/v1';

// type API_ENDPOINTS = {
//     LOGIN: string;
//     FORGET_PASSWORD: string;
//     REGISTER: string;
//     USER_PROFILE_DATA: string;
//     GET_USER_PROFILE: string;
//     RESET_PASSWORD: string;
//     VERIFY_TOKEN: string;
//     CHANGE_PASSWORD: string;
//     CHANGE_SETTING: string;
//     GET_USER_SETTING: string;
//     UPDATE_PASSWORD: string;
//     [key: string]: string;
// };

const API_ENDPOINTS = {
    // LOGIN: URL + '/agents/login',
    ACTIVATE_DEACTIVATE: URL + '/agent/account-action',
    GET_PROFILE_DETAILS: URL + '/agent',
    SEARCH_AGENCIES: URL + '/other/search-agency',
    // VALIDATE_EMAIL: URL + '/auth/validate-email',
    // VALIDATE_USERNAME: URL + '/auth/validate-username',
    // REGISTRATION: URL + '/auth/register',
    // EMAIL_VERIFICATION: URL + '/user/auth/verify-email',
    // FORGETPASSWORD: URL + '/auth/forgot-password',
    // FORGETOTP_VERIFY: URL + '/auth/verify-otp',
    // ACCOUNTCREATION_OTP_VERIFY: URL + '/auth/account-verify',
    RESET_PASSWORD: URL + '/auth/reset-password',
    // RESEND_OTP: URL + '/auth/resend-otp',
    LOGOUT: URL + '/auth/logout',
    // GET_USER_PROFILE: URL + '/auth/get-user-profile',
    // UPDATE_PROFILE: URL + '/auth/update-profile',
    // FORGET_PASSWORD: URL + '/user/auth/forgot-password',
    // USER_PROFILE_DATA: URL + '/profile/user_profile/update',
    // REGISTER: URL + '/user/auth/register',
    // VERIFY_TOKEN: URL + '/user/auth/verify-token',
    // CHANGE_PASSWORD: URL + '/user/auth/change-password',
    // GET_USER_SETTING: URL + '/profile/user_setting/get_user_setting',
    // CHANGE_SETTING: URL + '/profile/user_setting/update_user_setting',
    // UPDATE_PASSWORD: URL + '/auth/password',
    GET_SESSION: URL + '/auth/session',
    // BECOME_AN_AGENT: URL + '/agents/profile-creation',
    UPDATE_PROFILE: URL + '/agent/profile',
    ALL_LOCATIONS: URL + '/location',

    // REGISTATION PROCESS FORM
    REGISTER_AGENT: URL + '/agent/auth/register',
    REGISTER_OTP_VERIFICATION: URL + '/agent/auth/account-verify',
    RESEND_REGISTER_OTP: URL + '/agent/auth/resend-otp',
    AGENT_FORGET_PASSWORD: URL + '/agent/auth/forgot-password',
    AGENT_RESET_PASSWORD: URL + '/agent/auth/reset-password',
    // RESET_PASSWORD_OTP: URL + '/agent/auth/verify-otp',
    LOGIN_AGENT: URL + '/agent/auth/login',
    GETUSER_TYPE: URL + '/agent/auth/get-user-type/',
    CHECK_RESUBMIT: URL + '/agent/auth/resubmit/',

    AGENT_APPLICATIONS: URL + '/agent/user-profile',

    GET_NATIONALITY: URL + '/nationalities',
    GET_UAE_CITIES: URL + '/nationalities/emirateOfOperation',
    ALL_PARENT_SERVICES: URL + '/type/services',
    // AGENT_ROLE_TYPE: URL + '/type/parent/',
    COMPLETE_PROFILE: URL + '/agent/complete-profile',
    COMPLETE_PROFILE_COMPANY: URL + '/agent/complete-company-profile',
    SEND_INVITAION: URL + '/agent/invite-team',
    SUBSCRIPTION_PACKAGES: URL + '/agent/subscription',
    GET_SERVICES_BY_IDS: URL + '/type/services/',

    GET_PROFILE: URL + '/agent/get-application',
    RESUBMIT_COMPANY_API: URL + '/agent/re-submit/application/company',
    RESUBMIT_AGENT_API: URL + '/agent/re-submit/application/agent',
    SAVE_TEMP_DATA: URL + '/agent/application-temp-data',
    UPLOAD_FILE: URL + '/documents',
};

export default API_ENDPOINTS;
