import type { Metadata } from "next";
import { Inter, Merriweather_Sans } from "next/font/google";
import "@/styles/globals.css";
import { Toaster } from "@/components/ui/sonner";
import { SessionProvider } from "@/shared/context/session-context";


const inter = Inter({
    variable: "--font-inter",
    subsets: ["latin"],
    weight: ["400", "500", "600", "700", "800", "900"],
});


const merriweatherSans = Merriweather_Sans({
    variable: "--font-merriweather-sans",
    subsets: ["latin"],
    weight: ["300", "400", "500", "600", "700", "800"],
    style: ["normal"],
});


export const metadata: Metadata = {
    title: "Find Any Agent",
    description: "Find Any Agent",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={`${inter.variable} ${merriweatherSans.variable} antialiased`}
            >
                <Toaster />
                <SessionProvider>
                    {children}
                </SessionProvider>
            </body>
        </html>
    );
}
