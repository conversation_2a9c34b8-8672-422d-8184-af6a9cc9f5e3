'use server';

import { TReview, TReviewLegacy, ReviewStats } from '../types/review.type';

// Legacy function for backward compatibility
export const getReviews = async (): Promise<{ data: TReviewLegacy[] }> => {
  // TODO: Replace with actual API call
  const reviews: TReviewLegacy[] = [
    {
      id: '1',
      rating: 5,
      review: 'This is a great property! I would highly recommend it.',
      user: {
        id: '1',
        name: '<PERSON>',
        image: 'https://randomuser.me/api/portraits/men/1.jpg',
      },
      property: {
        id: '1',
        name: 'Modern Apartment in Downtown',
      },
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      rating: 4,
      review: 'A very comfortable and clean place. The host was very friendly.',
      user: {
        id: '2',
        name: '<PERSON>',
        image: 'https://randomuser.me/api/portraits/women/2.jpg',
      },
      property: {
        id: '2',
        name: 'Cozy Cottage in the Woods',
      },
      createdAt: new Date().toISOString(),
    },
  ];

  return { data: reviews };
};

// New management functions
export const  getReviewsForManagement = async (): Promise<{ data: TReview[] }> => {
  // TODO: Replace with actual API call
  const reviews: TReview[] = [
    {
      id: '1',
      reviewerName: 'Ahmed Hassan',
      reviewerEmail: '<EMAIL>',
      targetType: 'agent',
      targetName: 'Luxury Villa in Marina',
      targetId: 'property-1',
      rating: 5,
      comment: 'Excellent service! Found my dream property quickly and the agent was very professional.',
      status: 'published',
      createdAt: new Date('2024-01-20').toISOString(),
      updatedAt: new Date('2024-01-20').toISOString(),
    },
    {
      id: '2',
      reviewerName: 'Sarah Johnson',
      reviewerEmail: '<EMAIL>',
      targetType: 'agency',
      targetName: 'Downtown Apartment Complex',
      targetId: 'property-2',
      rating: 4,
      comment: 'Great location and amenities. The staff was helpful throughout the process.',
      status: 'published',
      createdAt: new Date('2024-01-18').toISOString(),
      updatedAt: new Date('2024-01-18').toISOString(),
    },
    {
      id: '3',
      reviewerName: 'Mike Wilson',
      reviewerEmail: '<EMAIL>',
      targetType: 'agent',
      targetName: 'Modern Office Space',
      targetId: 'property-3',
      rating: 2,
      comment: 'Not satisfied with the service provided. Issues with communication.',
      status: 'flagged',
      moderationRequest: {
        reason: 'Inappropriate language',
        requestedBy: 'admin',
        requestDate: new Date('2024-01-17').toISOString(),
        notes: 'Contains potentially offensive content'
      },
      createdAt: new Date('2024-01-17').toISOString(),
      updatedAt: new Date('2024-01-17').toISOString(),
    },
    {
      id: '4',
      reviewerName: 'Emily Davis',
      reviewerEmail: '<EMAIL>',
      targetType: 'agency',
      targetName: 'Beachfront Villa',
      targetId: 'property-4',
      rating: 1,
      comment: 'Terrible experience. Property was not as described.',
      status: 'hidden',
      hiddenReason: 'Spam content',
      createdAt: new Date('2024-01-15').toISOString(),
      updatedAt: new Date('2024-01-15').toISOString(),
    },
  ];

  return { data: reviews };
};

export const getReviewStats = async (): Promise<{ data: ReviewStats }> => {
  // TODO: Replace with actual API call
  const stats: ReviewStats = {
    total: 150,
    published: 120,
    pending: 15,
    flagged: 8,
    hidden: 7,
    averageRating: 4.2,
  };

  return { data: stats };
};

export const updateReviewStatus = async (
  reviewId: string, 
  status: TReview['status']
): Promise<{ success: boolean; message: string }> => {
  // TODO: Replace with actual API call
  console.log(`Updating review ${reviewId} status to ${status}`);
  
  return {
    success: true,
    message: `Review status updated to ${status}`
  };
};

export const flagReview = async (
  reviewId: string, 
  reason: string
): Promise<{ success: boolean; message: string }> => {
  // TODO: Replace with actual API call
  console.log(`Flagging review ${reviewId} with reason: ${reason}`);
  
  return {
    success: true,
    message: 'Review has been flagged for moderation'
  };
};

export const hideReview = async (
  reviewId: string, 
  reason: string
): Promise<{ success: boolean; message: string }> => {
  // TODO: Replace with actual API call
  console.log(`Hiding review ${reviewId} with reason: ${reason}`);
  
  return {
    success: true,
    message: 'Review has been hidden'
  };
};

export const publishReview = async (
  reviewId: string
): Promise<{ success: boolean; message: string }> => {
  // TODO: Replace with actual API call
  console.log(`Publishing review ${reviewId}`);
  
  return {
    success: true,
    message: 'Review has been published'
  };
};

export const deleteReview = async (
  reviewId: string
): Promise<{ success: boolean; message: string }> => {
  // TODO: Replace with actual API call
  console.log(`Deleting review ${reviewId}`);
  
  return {
    success: true,
    message: 'Review has been deleted'
  };
};

export const addNoteToReview = async (
  reviewId: string, 
  note: string
): Promise<{ success: boolean; message: string }> => {
  // TODO: Replace with actual API call
  console.log(`Adding note to review ${reviewId}: ${note}`);
  
  return {
    success: true,
    message: 'Note has been added to the review'
  };
};
