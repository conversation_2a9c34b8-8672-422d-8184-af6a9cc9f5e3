import { Reply, Flag } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import SurfaceCard from '@/shared/components/surface-card';
import { TReview } from '../types/review.type';
import ReviewStars from './review-stars';
import ReviewActionsDropdown from './review-actions-dropdown';

interface ReviewCardProps {
  review: TReview;
  onStatusChange: (id: string, status: TReview['status']) => void;
  onFlag: (id: string, reason: string) => void;
  onRestore: (id: string) => void;
  onHide: (id: string, reason: string) => void;
  onPublish: (id: string) => void;
  onViewDetails: (id: string) => void;
  onAddReview: () => void;
  onAddNote?: (id: string, note: string) => void;
  onReply?: (id: string) => void;
}

const ReviewCard = ({
  review,
  onStatusChange,
  onFlag,
  onRestore,
  onHide,
  onPublish,
  onViewDetails,
  onAddReview,
  onAddNote,
  onReply,
}: ReviewCardProps) => {
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'published':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'flagged':
        return 'destructive';
      case 'hidden':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <SurfaceCard className="w-full p-4">
      <div className="border-l-4 border-blue-500 pl-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-base">{review.reviewerName}</h3>
              <ReviewStars rating={review.rating} showNumber={false} />
              <Badge variant={getStatusBadgeVariant(review.status)}>
                {review.status}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground mb-1">
              {review.targetName}
            </div>
            <div className="text-xs text-muted-foreground mb-3">
              {formatDate(review.createdAt)}
            </div>
            <p className="text-sm text-gray-700 leading-relaxed">
              {review.comment}
            </p>
          </div>
          <div className="flex items-center gap-2 ml-4 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onReply?.(review.id)}
            >
              <Reply className="w-4 h-4 mr-1" />
              Reply
            </Button>
            <ReviewActionsDropdown
              review={review}
              onStatusChange={onStatusChange}
              onFlag={onFlag}
              onRestore={onRestore}
              onHide={onHide}
              onPublish={onPublish}
              onViewDetails={onViewDetails}
              onAddReview={onAddReview}
              onAddNote={onAddNote}
            />
          </div>
        </div>
      </div>
    </SurfaceCard>
  );
};

export default ReviewCard;
