'use client';

import { useEffect, useState } from 'react';
import { TReview } from '../types/review.type';
import { getReviewsForManagement } from '../actions/reviews.action';
import { useReviewsHandlers } from '../lib/use-reviews-handlers';
import ReviewsStatsGrid from './reviews-stats-grid';
import ReviewsFilters from './reviews-filters';
import ReviewsCardsList from './reviews-cards-list';

const ReviewsManagementContent = () => {
  const [reviews, setReviews] = useState<TReview[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        const { data } = await getReviewsForManagement();
        setReviews(data);
      } catch (error) {
        console.error('Failed to fetch reviews:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, []);

  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    selectedReview,
    setSelectedReview,
    moderationResponse,
    setModerationResponse,
    filteredReviews,
    stats,
    handleAddNote,
    handleDeleteReview,
    handleStatusChange,
    handleFlag,
    handleHide,
    handleRestore,
    handlePublish,
    handleModerationResponse,
    handleDropdownViewDetails,
    handleDropdownAddReview
  } = useReviewsHandlers(reviews);

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading reviews...</div>;
  }

  return (
    <div className="space-y-6">
      <ReviewsStatsGrid stats={stats} />
      <ReviewsFilters 
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        typeFilter={typeFilter}
        onTypeFilterChange={setTypeFilter}
      />
      <ReviewsCardsList
        reviews={reviews}
        filteredReviews={filteredReviews}
        onStatusChange={handleStatusChange}
        onFlag={handleFlag}
        onRestore={handleRestore}
        onHide={handleHide}
        onPublish={handlePublish}
        onViewDetails={handleDropdownViewDetails}
        onAddReview={handleDropdownAddReview}
        onAddNote={handleAddNote}
        onReply={(id) => console.log('Reply to review:', id)}
      />
    </div>
  );
};

export default ReviewsManagementContent;
